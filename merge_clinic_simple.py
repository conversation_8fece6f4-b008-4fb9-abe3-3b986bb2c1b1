# -*- coding: utf-8 -*-
"""
门诊日志与挂号明细合并程序 - 简化版
直接处理指定的文件
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys

def merge_clinic_data_simple(clinic_log_file, registration_file, output_file=None):
    """
    简化版合并函数
    """
    try:
        # 读取文件
        print(f"读取门诊日志: {clinic_log_file}")
        clinic_df = pd.read_excel(clinic_log_file)
        
        print(f"读取挂号明细: {registration_file}")
        registration_df = pd.read_excel(registration_file)
        
        print(f"门诊日志行数: {len(clinic_df)}")
        print(f"挂号明细行数: {len(registration_df)}")
        
        # 创建结果DataFrame
        result_df = clinic_df.copy()
        
        # 找到"就诊时间(医嘱)"列的位置
        if '就诊时间(医嘱)' not in result_df.columns:
            print("错误: 门诊日志中未找到'就诊时间(医嘱)'列")
            return False
        
        clinic_time_col_index = result_df.columns.get_loc('就诊时间(医嘱)')
        
        # 在"就诊时间(医嘱)"列后插入"挂号时间"列
        cols = list(result_df.columns)
        cols.insert(clinic_time_col_index + 1, '挂号时间')
        result_df['挂号时间'] = np.nan
        result_df = result_df.reindex(columns=cols)
        
        # 执行匹配
        matched_count = 0
        
        for idx, clinic_row in clinic_df.iterrows():
            # 获取门诊日志的匹配字段
            clinic_reg_no = str(clinic_row.get('登记号', '')).strip()
            clinic_name = str(clinic_row.get('患者姓名', '')).strip()
            
            # 处理就诊日期
            clinic_visit_date = clinic_row.get('就诊日期')
            if pd.isna(clinic_visit_date):
                continue
                
            # 标准化日期（只保留日期部分）
            try:
                if isinstance(clinic_visit_date, str):
                    clinic_date = pd.to_datetime(clinic_visit_date).date()
                else:
                    clinic_date = pd.to_datetime(clinic_visit_date).date()
            except:
                continue
            
            # 在挂号明细中查找匹配项
            for reg_idx, reg_row in registration_df.iterrows():
                reg_no = str(reg_row.get('登记号', '')).strip()
                reg_name = str(reg_row.get('患者姓名', '')).strip()
                reg_time = reg_row.get('挂号时间')
                
                if pd.isna(reg_time):
                    continue
                
                # 提取挂号时间的日期部分
                try:
                    if isinstance(reg_time, str):
                        reg_date = pd.to_datetime(reg_time).date()
                    else:
                        reg_date = pd.to_datetime(reg_time).date()
                except:
                    continue
                
                # 检查匹配条件
                if (clinic_reg_no == reg_no and 
                    clinic_name == reg_name and 
                    clinic_date == reg_date):
                    
                    result_df.at[idx, '挂号时间'] = reg_time
                    matched_count += 1
                    break  # 找到匹配项后跳出内层循环
        
        print(f"成功匹配 {matched_count} 条记录")
        
        # 保存结果
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"门诊日志合并结果_{timestamp}.xlsx"
        
        result_df.to_excel(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 示例用法
    if len(sys.argv) >= 3:
        clinic_file = sys.argv[1]
        registration_file = sys.argv[2]
        output_file = sys.argv[3] if len(sys.argv) > 3 else None
        
        merge_clinic_data_simple(clinic_file, registration_file, output_file)
    else:
        print("使用方法:")
        print("python merge_clinic_simple.py <门诊日志文件> <挂号明细文件> [输出文件]")
        print("\n或者修改下面的文件名直接运行:")
        
        # 这里可以直接指定文件名
        clinic_log_file = "门诊日志.xlsx"  # 请修改为实际的门诊日志文件名
        registration_file = "挂号明细.xlsx"  # 请修改为实际的挂号明细文件名
        
        print(f"将处理: {clinic_log_file} 和 {registration_file}")
        merge_clinic_data_simple(clinic_log_file, registration_file)

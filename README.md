# 门诊日志与挂号明细合并程序

## 功能说明

本程序用于将挂号明细Excel文件中的挂号时间信息合并到门诊日志Excel文件中。

### 匹配条件
程序会根据以下条件进行匹配：
1. **登记号**：挂号明细中的登记号与门诊日志中的登记号完全相同
2. **患者姓名**：挂号明细中的患者姓名与门诊日志中的患者姓名完全相同  
3. **日期**：挂号明细中"挂号时间"列的日期与门诊日志中"就诊日期"的日期相同

### 处理结果
- 将匹配到的挂号时间插入到门诊日志的"就诊时间(医嘱)"列后面
- 生成新的Excel文件，包含原门诊日志的所有数据加上新增的"挂号时间"列

## 文件说明

### 1. merge_clinic_data.py（完整版）
- 交互式程序，会列出当前目录下的Excel文件供用户选择
- 包含详细的错误检查和日志输出
- 适合初次使用或需要详细信息的场景

### 2. merge_clinic_simple.py（简化版）
- 可以通过命令行参数或直接修改代码指定文件名
- 处理逻辑更简洁
- 适合批量处理或自动化场景

## 使用方法

### 方法一：使用完整版程序
```bash
python merge_clinic_data.py
```
程序会自动列出当前目录下的Excel文件，按提示选择即可。

### 方法二：使用简化版程序（命令行）
```bash
python merge_clinic_simple.py 门诊日志.xlsx 挂号明细.xlsx [输出文件名.xlsx]
```

### 方法三：使用简化版程序（修改代码）
1. 打开 `merge_clinic_simple.py` 文件
2. 修改文件末尾的文件名：
   ```python
   clinic_log_file = "你的门诊日志文件名.xlsx"
   registration_file = "你的挂号明细文件名.xlsx"
   ```
3. 运行程序：
   ```bash
   python merge_clinic_simple.py
   ```

## 必要条件

### Excel文件列名要求

**门诊日志文件必须包含以下列：**
- 登记号
- 患者姓名
- 就诊日期
- 就诊时间(医嘱)

**挂号明细文件必须包含以下列：**
- 登记号
- 患者姓名
- 挂号时间

### 环境要求
- Python 3.6+
- pandas库
- openpyxl库（用于Excel文件处理）

### 安装依赖
```bash
pip install pandas openpyxl
```

## 输出文件

程序会生成一个新的Excel文件，文件名格式为：
`门诊日志合并结果_YYYYMMDD_HHMMSS.xlsx`

输出文件包含：
- 原门诊日志的所有列
- 在"就诊时间(医嘱)"列后新增"挂号时间"列
- 匹配成功的记录会显示对应的挂号时间
- 未匹配的记录在"挂号时间"列显示为空

## 注意事项

1. **数据格式**：确保登记号和患者姓名的格式一致（去除多余空格）
2. **日期格式**：程序会自动处理常见的日期格式，只比较日期部分（忽略时间）
3. **文件路径**：确保Excel文件在程序运行目录下，或提供完整路径
4. **备份数据**：建议在处理前备份原始文件
5. **重复匹配**：如果一条门诊记录匹配到多条挂号记录，会使用第一条匹配的记录

## 故障排除

### 常见错误及解决方法

1. **"未找到必要列"错误**
   - 检查Excel文件的列名是否与要求完全一致
   - 注意列名中的空格和特殊字符

2. **"读取文件失败"错误**
   - 确认文件路径正确
   - 确认Excel文件未被其他程序占用
   - 检查文件是否损坏

3. **匹配数量为0**
   - 检查登记号格式是否一致
   - 检查患者姓名是否完全相同
   - 检查日期格式是否正确

4. **程序运行缓慢**
   - 对于大文件，处理时间较长是正常的
   - 可以通过控制台输出监控进度

## 联系支持

如遇到问题，请检查：
1. 文件格式是否符合要求
2. 必要的Python库是否已安装
3. 数据中是否存在格式不一致的情况

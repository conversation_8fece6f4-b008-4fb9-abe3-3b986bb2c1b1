# -*- coding: utf-8 -*-
"""
门诊日志与挂号明细合并程序 - 包含医生匹配
匹配条件：登记号、患者姓名、日期、医生姓名都相同
"""

import pandas as pd
from datetime import datetime
import os

def merge_clinic_with_doctor():
    """
    包含医生匹配的合并函数
    """
    print("门诊日志与挂号明细合并程序 - 包含医生匹配")
    print("=" * 60)
    print("匹配条件：登记号 + 患者姓名 + 日期 + 医生姓名")
    print("=" * 60)
    
    # 获取当前目录下的Excel文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~$')]
    
    if len(excel_files) < 2:
        print("当前目录下Excel文件不足，请确保有门诊日志和挂号明细文件")
        return False
    
    print("当前目录下的Excel文件:")
    for i, file in enumerate(excel_files):
        print(f"{i+1}. {file}")
    
    # 智能选择文件
    clinic_file = None
    registration_file = None
    
    # 优先选择不包含"合并结果"的文件作为原始文件
    original_files = [f for f in excel_files if '合并结果' not in f]
    result_files = [f for f in excel_files if '合并结果' in f]
    
    if len(original_files) >= 2:
        # 如果有原始文件，优先使用原始文件
        for file in original_files:
            file_lower = file.lower()
            if any(keyword in file_lower for keyword in ['门诊日志', '日志', 'clinic', 'log']):
                clinic_file = file
            elif any(keyword in file_lower for keyword in ['挂号明细', '挂号', '明细', 'registration', 'register']):
                registration_file = file
        
        if not clinic_file or not registration_file:
            clinic_file = original_files[0]
            registration_file = original_files[1]
    else:
        # 使用所有文件进行匹配
        for file in excel_files:
            file_lower = file.lower()
            if any(keyword in file_lower for keyword in ['门诊日志', '日志', 'clinic', 'log']):
                clinic_file = file
            elif any(keyword in file_lower for keyword in ['挂号明细', '挂号', '明细', 'registration', 'register']):
                registration_file = file
        
        if not clinic_file or not registration_file:
            clinic_file = excel_files[0]
            registration_file = excel_files[1]
    
    print(f"\n选择的文件:")
    print(f"门诊日志文件: {clinic_file}")
    print(f"挂号明细文件: {registration_file}")
    
    try:
        # 读取文件
        print("\n正在读取门诊日志...")
        clinic_df = pd.read_excel(clinic_file)
        print(f"门诊日志行数: {len(clinic_df)}")
        
        print("正在读取挂号明细...")
        registration_df = pd.read_excel(registration_file)
        print(f"挂号明细行数: {len(registration_df)}")
        
        # 确定列名
        name_col_clinic = '姓名' if '姓名' in clinic_df.columns else '患者姓名'
        name_col_registration = '患者姓名'
        
        # 确定医生列名
        doctor_col_clinic = '医生'
        doctor_col_registration = None
        if '看诊医生' in registration_df.columns:
            doctor_col_registration = '看诊医生'
        elif '医生(号别)' in registration_df.columns:
            doctor_col_registration = '医生(号别)'
        else:
            print("❌ 挂号明细中未找到医生相关列")
            return False
        
        print(f"\n使用的列名映射:")
        print(f"门诊日志患者姓名列: {name_col_clinic}")
        print(f"挂号明细患者姓名列: {name_col_registration}")
        print(f"门诊日志医生列: {doctor_col_clinic}")
        print(f"挂号明细医生列: {doctor_col_registration}")
        
        # 检查必要列
        required_clinic_cols = ['登记号', name_col_clinic, '就诊日期', '就诊时间(医嘱)', doctor_col_clinic]
        required_registration_cols = ['登记号', name_col_registration, '挂号时间', doctor_col_registration]
        
        missing_clinic = [col for col in required_clinic_cols if col not in clinic_df.columns]
        missing_registration = [col for col in required_registration_cols if col not in registration_df.columns]
        
        if missing_clinic:
            print(f"门诊日志缺少必要列: {missing_clinic}")
            return False
        if missing_registration:
            print(f"挂号明细缺少必要列: {missing_registration}")
            return False
        
        # 数据预处理
        print("\n正在进行数据预处理...")
        
        # 处理门诊日志
        clinic_work = clinic_df.copy()
        clinic_work['登记号_str'] = clinic_work['登记号'].astype(str).str.strip()
        clinic_work['姓名_str'] = clinic_work[name_col_clinic].astype(str).str.strip()
        clinic_work['就诊日期_date'] = pd.to_datetime(clinic_work['就诊日期']).dt.date
        clinic_work['医生_str'] = clinic_work[doctor_col_clinic].astype(str).str.strip()
        
        # 处理挂号明细
        registration_work = registration_df.copy()
        registration_work['登记号_str'] = registration_work['登记号'].astype(str).str.strip()
        registration_work['姓名_str'] = registration_work[name_col_registration].astype(str).str.strip()
        registration_work['挂号日期_date'] = pd.to_datetime(registration_work['挂号时间']).dt.date
        
        # 处理医生信息
        if doctor_col_registration == '医生(号别)':
            # 从"医生(号别)"中提取医生姓名
            registration_work['医生_str'] = registration_work[doctor_col_registration].astype(str).str.strip()
            registration_work['医生_str'] = registration_work['医生_str'].str.split('(').str[0].str.strip()
        else:
            registration_work['医生_str'] = registration_work[doctor_col_registration].astype(str).str.strip()
        
        # 创建合并键（包含医生信息）
        clinic_work['merge_key'] = (clinic_work['登记号_str'] + '|' + 
                                   clinic_work['姓名_str'] + '|' + 
                                   clinic_work['就诊日期_date'].astype(str) + '|' +
                                   clinic_work['医生_str'])
        
        registration_work['merge_key'] = (registration_work['登记号_str'] + '|' + 
                                        registration_work['姓名_str'] + '|' + 
                                        registration_work['挂号日期_date'].astype(str) + '|' +
                                        registration_work['医生_str'])
        
        # 去重挂号明细
        registration_unique = registration_work.drop_duplicates(subset=['merge_key'], keep='first')
        print(f"去重后的挂号明细行数: {len(registration_unique)}")
        
        # 检查门诊日志是否已经包含挂号时间列
        if '挂号时间' in clinic_work.columns:
            print("⚠️  门诊日志已包含'挂号时间'列，将重新匹配并更新")
            # 删除原有的挂号时间列
            clinic_work = clinic_work.drop(columns=['挂号时间'])

        # 执行合并
        print("正在执行数据合并...")
        registration_merge = registration_unique[['merge_key', '挂号时间']].copy()
        result_df = clinic_work.merge(registration_merge, on='merge_key', how='left')

        # 统计匹配结果
        if '挂号时间' in result_df.columns:
            matched_count = result_df['挂号时间'].notna().sum()
            print(f"成功匹配 {matched_count} 条记录")
        else:
            matched_count = 0
            print("❌ 合并失败，未找到挂号时间列")

        # 清理临时列
        result_df = result_df.drop(columns=['登记号_str', '姓名_str', '就诊日期_date', '医生_str', 'merge_key'])

        # 重新排列列顺序
        if '挂号时间' in result_df.columns:
            clinic_time_col_index = list(result_df.columns).index('就诊时间(医嘱)')
            cols = list(result_df.columns)
            cols.remove('挂号时间')
            cols.insert(clinic_time_col_index + 1, '挂号时间')
            result_df = result_df[cols]
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"门诊日志合并结果_含医生匹配_{timestamp}.xlsx"
        
        print(f"\n正在保存结果到: {output_file}")
        result_df.to_excel(output_file, index=False)
        
        print(f"\n✅ 处理完成！")
        print(f"📊 处理统计:")
        print(f"   - 门诊日志总数: {len(clinic_df)}")
        print(f"   - 挂号明细总数: {len(registration_df)}")
        print(f"   - 成功匹配数: {matched_count}")
        print(f"   - 匹配率: {matched_count/len(clinic_df)*100:.2f}%")
        print(f"📁 输出文件: {output_file}")
        print(f"🔍 匹配条件: 登记号 + 患者姓名 + 日期 + 医生姓名")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    merge_clinic_with_doctor()

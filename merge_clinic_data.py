# -*- coding: utf-8 -*-
"""
门诊日志与挂号明细合并程序
根据登记号、患者姓名和日期匹配，将挂号时间插入门诊日志
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

def read_excel_file(file_path, sheet_name=None):
    """
    读取Excel文件
    """
    try:
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
        else:
            df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
        print(f"数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"读取文件失败 {file_path}: {str(e)}")
        return None

def normalize_date(date_value):
    """
    标准化日期格式，只保留日期部分
    """
    if pd.isna(date_value):
        return None
    
    if isinstance(date_value, str):
        try:
            # 尝试解析字符串日期
            date_obj = pd.to_datetime(date_value)
            return date_obj.date()
        except:
            return None
    elif isinstance(date_value, datetime):
        return date_value.date()
    elif hasattr(date_value, 'date'):
        return date_value.date()
    else:
        try:
            date_obj = pd.to_datetime(date_value)
            return date_obj.date()
        except:
            return None

def merge_clinic_data(clinic_log_file, registration_file, output_file=None):
    """
    合并门诊日志和挂号明细数据
    
    参数:
    clinic_log_file: 门诊日志Excel文件路径
    registration_file: 挂号明细Excel文件路径
    output_file: 输出文件路径（可选）
    """
    
    # 读取门诊日志
    print("正在读取门诊日志...")
    clinic_df = read_excel_file(clinic_log_file)
    if clinic_df is None:
        return False
    
    # 读取挂号明细
    print("正在读取挂号明细...")
    registration_df = read_excel_file(registration_file)
    if registration_df is None:
        return False
    
    print("\n门诊日志列名:")
    for i, col in enumerate(clinic_df.columns):
        print(f"{i+1}. {col}")
    
    print("\n挂号明细列名:")
    for i, col in enumerate(registration_df.columns):
        print(f"{i+1}. {col}")
    
    # 检查必要的列是否存在，并进行列名映射
    # 门诊日志的列名映射
    clinic_col_mapping = {}
    if '姓名' in clinic_df.columns:
        clinic_col_mapping['患者姓名'] = '姓名'
    elif '患者姓名' in clinic_df.columns:
        clinic_col_mapping['患者姓名'] = '患者姓名'

    required_clinic_cols = ['登记号', '患者姓名', '就诊日期', '就诊时间(医嘱)']
    required_registration_cols = ['登记号', '患者姓名', '挂号时间']

    # 检查门诊日志必要列
    missing_clinic_cols = []
    for col in required_clinic_cols:
        if col == '患者姓名':
            if '姓名' not in clinic_df.columns and '患者姓名' not in clinic_df.columns:
                missing_clinic_cols.append(col)
        elif col not in clinic_df.columns:
            missing_clinic_cols.append(col)

    missing_registration_cols = [col for col in required_registration_cols if col not in registration_df.columns]
    
    if missing_clinic_cols:
        print(f"门诊日志缺少必要列: {missing_clinic_cols}")
        return False
    
    if missing_registration_cols:
        print(f"挂号明细缺少必要列: {missing_registration_cols}")
        return False
    
    # 创建门诊日志的副本
    result_df = clinic_df.copy()
    
    # 添加挂号时间列（在就诊时间(医嘱)列后面）
    clinic_time_col_index = result_df.columns.get_loc('就诊时间(医嘱)')
    
    # 创建新的列顺序
    cols = list(result_df.columns)
    cols.insert(clinic_time_col_index + 1, '挂号时间')
    
    # 初始化挂号时间列
    result_df['挂号时间'] = np.nan
    
    # 重新排列列顺序
    result_df = result_df.reindex(columns=cols)
    
    # 标准化日期格式用于匹配
    print("\n正在处理日期格式...")
    clinic_df['就诊日期_标准'] = clinic_df['就诊日期'].apply(normalize_date)
    registration_df['挂号日期_标准'] = registration_df['挂号时间'].apply(normalize_date)
    
    # 执行匹配
    print("正在执行数据匹配...")
    matched_count = 0

    # 确定患者姓名列名
    name_col = '姓名' if '姓名' in clinic_df.columns else '患者姓名'

    for idx, clinic_row in clinic_df.iterrows():
        clinic_reg_no = str(clinic_row['登记号']).strip() if pd.notna(clinic_row['登记号']) else ''
        clinic_name = str(clinic_row[name_col]).strip() if pd.notna(clinic_row[name_col]) else ''
        clinic_date = clinic_row['就诊日期_标准']
        
        if not clinic_reg_no or not clinic_name or clinic_date is None:
            continue
        
        # 在挂号明细中查找匹配项
        matching_registrations = registration_df[
            (registration_df['登记号'].astype(str).str.strip() == clinic_reg_no) &
            (registration_df['患者姓名'].astype(str).str.strip() == clinic_name) &
            (registration_df['挂号日期_标准'] == clinic_date)
        ]
        
        if not matching_registrations.empty:
            # 如果有多个匹配项，取第一个
            registration_time = matching_registrations.iloc[0]['挂号时间']
            result_df.at[idx, '挂号时间'] = registration_time
            matched_count += 1
    
    print(f"\n匹配完成！成功匹配 {matched_count} 条记录")
    
    # 生成输出文件名
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"门诊日志合并结果_{timestamp}.xlsx"
    
    # 保存结果
    try:
        result_df.to_excel(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        return True
    except Exception as e:
        print(f"保存文件失败: {str(e)}")
        return False

def main():
    """
    主函数 - 自动识别文件
    """
    print("门诊日志与挂号明细合并程序")
    print("=" * 50)

    # 获取当前目录下的Excel文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~$')]

    if len(excel_files) < 2:
        print("当前目录下Excel文件不足，请确保有门诊日志和挂号明细文件")
        return

    print("当前目录下的Excel文件:")
    for i, file in enumerate(excel_files):
        print(f"{i+1}. {file}")

    # 自动识别文件类型
    clinic_file = None
    registration_file = None

    # 根据文件名关键词自动识别
    for file in excel_files:
        file_lower = file.lower()
        if any(keyword in file_lower for keyword in ['门诊日志', '日志', 'clinic', 'log']):
            clinic_file = file
        elif any(keyword in file_lower for keyword in ['挂号明细', '挂号', '明细', 'registration', 'register']):
            registration_file = file

    # 如果自动识别失败，使用第一个文件作为门诊日志，第二个作为挂号明细
    if not clinic_file or not registration_file:
        print("\n无法自动识别文件类型，使用默认分配:")
        clinic_file = excel_files[0] if len(excel_files) > 0 else None
        registration_file = excel_files[1] if len(excel_files) > 1 else None

    if clinic_file and registration_file:
        print(f"\n门诊日志文件: {clinic_file}")
        print(f"挂号明细文件: {registration_file}")

        # 执行合并
        success = merge_clinic_data(clinic_file, registration_file)

        if success:
            print("\n程序执行完成！")
        else:
            print("\n程序执行失败，请检查文件格式和列名")
    else:
        print("无法找到足够的文件进行处理")

if __name__ == "__main__":
    main()

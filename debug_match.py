# -*- coding: utf-8 -*-
"""
调试匹配程序 - 专门检查林映锋的匹配情况
"""

import pandas as pd

def debug_lingyingfeng_match():
    print("调试林映锋的匹配情况")
    print("=" * 50)
    
    # 读取文件
    clinic_df = pd.read_excel('门诊日志合并结果_20250919_104528.xlsx')
    registration_df = pd.read_excel('挂号明细合并结果_20250919_092054.xlsx')
    
    # 查找林映锋在门诊日志中的记录
    clinic_lingyingfeng = clinic_df[(clinic_df['姓名'] == '林映锋') & 
                                   (clinic_df['就诊日期'] == '2025-01-09') & 
                                   (clinic_df['医生'] == '李泽辉')]
    
    print("门诊日志中林映锋的记录:")
    for idx, row in clinic_lingyingfeng.iterrows():
        print(f"登记号: {row['登记号']}")
        print(f"姓名: {row['姓名']}")
        print(f"医生: {row['医生']}")
        print(f"就诊日期: {row['就诊日期']}")
        print(f"就诊日期类型: {type(row['就诊日期'])}")
        
        # 标准化日期
        clinic_date = pd.to_datetime(row['就诊日期']).date()
        print(f"标准化就诊日期: {clinic_date}")
        
        # 创建合并键
        clinic_key = f"{str(row['登记号']).strip()}|{str(row['姓名']).strip()}|{clinic_date}|{str(row['医生']).strip()}"
        print(f"门诊日志合并键: {clinic_key}")
        print("-" * 30)
    
    # 查找林映锋在挂号明细中的记录
    registration_lingyingfeng = registration_df[(registration_df['患者姓名'] == '林映锋') & 
                                               (registration_df['就诊日期'] == '2025-01-09')]
    
    print("\n挂号明细中林映锋的记录:")
    for idx, row in registration_lingyingfeng.iterrows():
        print(f"登记号: {row['登记号']}")
        print(f"患者姓名: {row['患者姓名']}")
        print(f"医生(号别): {row['医生(号别)']}")
        print(f"就诊日期: {row['就诊日期']}")
        print(f"挂号时间: {row['挂号时间']}")
        print(f"挂号时间类型: {type(row['挂号时间'])}")
        
        # 处理医生信息
        doctor_raw = str(row['医生(号别)']).strip()
        doctor_extracted = doctor_raw.split('(')[0].strip()
        print(f"提取的医生姓名: {doctor_extracted}")
        
        # 标准化日期
        registration_date = pd.to_datetime(row['挂号时间']).date()
        print(f"标准化挂号日期: {registration_date}")
        
        # 创建合并键
        registration_key = f"{str(row['登记号']).strip()}|{str(row['患者姓名']).strip()}|{registration_date}|{doctor_extracted}"
        print(f"挂号明细合并键: {registration_key}")
        print("-" * 30)
    
    print("\n匹配分析:")
    print("需要匹配的条件:")
    print("1. 登记号: 22217528")
    print("2. 姓名: 林映锋") 
    print("3. 日期: 2025-01-09")
    print("4. 医生: 李泽辉")

if __name__ == "__main__":
    debug_lingyingfeng_match()
